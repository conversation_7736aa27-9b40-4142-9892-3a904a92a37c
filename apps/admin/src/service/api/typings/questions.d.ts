declare namespace QuestionsApi {
/**
 * UwooAgent.Model.AI.GetAgentModelInfoOutput，获取智能体模型信息输出
 */
  interface AgentModelInfoResponse {
    /**
     * Id
     */
    Id: string
    /**
     * 是否支持上下文
     */
    IsContext: boolean
    /**
     * 模型描述
     */
    ModelDescribe: string
    /**
     * 模型名称
     */
    ModelName: string
  }

  /**
   * UwooAgent.Model.AI.CreateQuestionInput，创建题目请求参数
   */
  interface CreateQuestionRequest {
    /**
     * 补充内容（出题要求）
     */
    AdditionalRequirements: string
    /**
     * Ai模型Id
     */
    AIModeId: string
    /**
     * 传 单元-章节名称（章节出题时使用）
     */
    ChapterIds: string[]
    /**
     * 难度等级ID
     */
    DifficultyId: string | null
    /**
     * 文件出题时使用的文件URL（附件出题时使用）
     */
    FileUrls: string[]
    /**
     * 年级
     */
    Grade: number
    /**
     * 知识点ID列表（知识点出题时使用）
     */
    KnowledgePointIds: string[]
    /**
     * 出题模式
     */
    Mode: 1 | 2 | 3 | 4
    /**
     * 出题数量
     */
    QuestionCount: number | null
    /**
     * 学习水平ID（也就是学习水平）
     */
    LearningLevelId: string | null
    /**
     * 题型ID列表（可多选）
     */
    QuestionTypeIds: number[] | null

    /**
     * 出题范围文本（文本出题时使用）
     */
    TextContent: string

  }

  interface GetDifficultyResponse {
    code: string
    text: string
    value: string
  }
  /**
   * Uwoo.Business.Base_Manage.Sys_ExamineListTreeDTO
   */
  interface GetLearningLevelResponse {
    children?: null | []
    Children?: any[] | null
    Id: string
    key: string
    Level: number | null
    ParentId: null | string
    Text: string
    title: string
    value: string
    Value: string
  }
  /**
   * UwooAgent.Model.AI.QuestionTypeDto，题型数据传输对象
   */
  interface GetQuestionTypesResponse {
    /**
     * 题型描述
     */
    Description: string
    /**
     * 题型ID
     */
    Id: number
    /**
     * 题型名称
     */
    Name: string
  }

  interface GetChapterListResponseRequest {
    /**
     * 年级
     */
    grade: string
    /**
     * 学期
     */
    term: number
    /**
     * 学年
     */
    year: number
  }

  /**
   * 根据学年，学期获取章节数据
   */
  interface GetChapterListResponse {
    ChapterId: string
    ChapterName: string
    ParentId: string
    Second: ChapterListItem[] | null
  }

  interface ChapterListItem {
    ChapterId: string
    ChapterName: string
    ParentId: string
    ResourceCount: number
    Third: string | null
  }
  // 批量保存到题库

  /**
   * UwooAgent.Model.AI.SaveBatchQuestionsToBankInput，批量保存到题库请求参数
   */
  interface SaveBatchQuestionsToBankInputRequest {
    /**
     * 章节ID
     */
    ChapterId: string | string[]
    /**
     * 难度ID
     */
    DifficultyId: string
    /**
     * 学习水平ID（出题方向ID）
     */
    LearningLevelId: string
    /**
     * 题目列表
     */
    Questions: GeneratedQuestion | GeneratedQuestion[] | null
  }

  /**
   * UwooAgent.Model.AI.AIGeneratedQuestion，AI生成的单个题目
   */
  interface GeneratedQuestion {
    /**
     * 答案解析
     */
    Analysis: string
    /**
     * 正确答案
     */
    Answer: string
    /**
     * 关联的章节列表（章节出题时使用）
     */
    Chapters: QuestionChapter[] | null
    /**
     * 关联的知识点列表（知识点出题时使用）
     */
    KnowledgePoints: QuestionKnowledgePoints[] | null
    /**
     * 选项（选择题、多项选择题、判断题需要）
     */
    Options: QuestionOption[] | null
    /**
     * 题型
     */
    QuestionType: string
    /**
     * 题型ID
     */
    QuestionTypeId: string
    /**
     * 题干
     */
    Title: string
  }

  /**
   * UwooAgent.Model.AI.QuestionChapter，题目关联的章节信息
   */
  interface QuestionChapter {
    /**
     * 章节名称
     */
    ChapterName: string
    /**
     * 章节ID
     */
    Id: string
  }

  /**
   * UwooAgent.Model.AI.QuestionKnowledgePoint，题目关联的知识点信息
   */
  interface QuestionKnowledgePoints {
    /**
     * 知识点内容
     */
    Content: string
    /**
     * 知识点ID
     */
    Id: string
    /**
     * 知识点层级
     */
    Level: number
  }

  /**
   * UwooAgent.Model.AI.AIQuestionOption，题目选项
   */
  interface QuestionOption {
    /**
     * 选项内容
     */
    Content: string
    /**
     * 选项标识（A、B、C、D）
     */
    Option: string
  }
  // ***********************// 出参数

  interface SaveBatchQuestionsToBankResponse {
    /**
     * 错误信息
     */
    ErrorMessage: null | string
    /**
     * 失败的题目数量
     */
    FailedCount: number
    /**
     * 保存成功的题目ID列表
     */
    SavedQuestionIds: string[] | null
    /**
     * 是否成功
     */
    Success: boolean
    /**
     * 成功保存的题目数量
     */
    SuccessCount: number
  }

  /**
   * UwooAgent.Model.AI.RegenerateQuestionInput，重新生成单题请求参数
   */
  interface RegenerateQuestionInputRequest {
    /**
     * AI模型ID
     */
    AIModeId: string
    /**
     * 难度等级名称（可选，如果不指定则使用原题目的难度）
     */
    DifficultyLevelName: string
    OriginalQuestion: GeneratedQuestion
    /**
     * 出题方向名称（可选，如果不指定则使用原题目的方向）
     */
    QuestionDirectionName: string
    /**
     * 用户要求（可选）
     */
    UserRequirement: string
  }

}
